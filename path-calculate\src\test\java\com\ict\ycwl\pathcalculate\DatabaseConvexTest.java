package com.ict.ycwl.pathcalculate;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ict.ycwl.pathcalculate.mapper.RouteMapper;
import com.ict.ycwl.pathcalculate.mapper.TransitDepotMapper;
import com.ict.ycwl.pathcalculate.pojo.Route;
import com.ict.ycwl.pathcalculate.pojo.TransitDepot;
import com.ict.ycwl.pathcalculate.utils.getColorUtils.Readroute;
import com.ict.ycwl.pathcalculate.utils.getColorUtils.Readboundary;
import com.ict.ycwl.pathcalculate.utils.getColorUtils.Networkfulling;
import com.ict.ycwl.pathcalculate.utils.BoundaryPoint;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.locationtech.jts.geom.Polygon;
import org.locationtech.jts.geom.Geometry;
import org.locationtech.jts.io.ParseException;

import java.io.IOException;
import java.util.*;

/**
 * 使用数据库真实数据的凸包测试
 */
@SpringBootTest
public class DatabaseConvexTest {

    @Autowired
    private RouteMapper routeMapper;
    
    @Autowired
    private TransitDepotMapper transitDepotMapper;

    @Test
    public void testRealDatabaseData() {
        System.out.println("=== 数据库真实数据测试 ===");
        
        try {
            // 1. 查询路线数据
            QueryWrapper<Route> routeQueryWrapper = new QueryWrapper<>();
            routeQueryWrapper.eq("is_delete", false);
            List<Route> routes = routeMapper.selectList(routeQueryWrapper);
            System.out.println("查询到路线数量: " + routes.size());
            
            // 2. 查询中转站数据
            QueryWrapper<TransitDepot> transitDepotQueryWrapper = new QueryWrapper<>();
            transitDepotQueryWrapper.eq("status", 1).eq("is_delete", 0);
            List<TransitDepot> transitDepots = transitDepotMapper.selectList(transitDepotQueryWrapper);
            System.out.println("查询到中转站数量: " + transitDepots.size());
            
            // 3. 构建polygonsGroup
            Map<Polygon, Integer> polygonsGroup = new LinkedHashMap<>();
            Readroute readroute = new Readroute();
            int validRoutes = 0;
            int invalidRoutes = 0;
            
            for (Route route : routes) {
                try {
                    // 查找对应的中转站
                    TransitDepot transitDepot = null;
                    for (TransitDepot td : transitDepots) {
                        if (td.getTransitDepotId().equals(route.getTransitDepotId())) {
                            transitDepot = td;
                            break;
                        }
                    }
                    
                    if (transitDepot == null) {
                        System.out.println("路线 " + route.getRouteName() + " 对应的中转站不存在或未启用");
                        invalidRoutes++;
                        continue;
                    }
                    
                    if (route.getConvex() == null || route.getConvex().trim().isEmpty()) {
                        System.out.println("路线 " + route.getRouteName() + " 的凸包数据为空");
                        invalidRoutes++;
                        continue;
                    }
                    
                    Polygon polygon = readroute.createPolygonFromLine(route.getConvex());
                    if (polygon != null && polygon.isValid()) {
                        polygonsGroup.put(polygon, transitDepot.getGroupId().intValue());
                        validRoutes++;
                        
                        if (validRoutes <= 5) { // 只打印前5个
                            System.out.println("路线 " + route.getRouteName() + 
                                             " -> 班组 " + transitDepot.getGroupId() + 
                                             ", 凸包坐标数: " + polygon.getCoordinates().length);
                        }
                    } else {
                        System.out.println("路线 " + route.getRouteName() + " 的凸包数据解析失败或无效");
                        invalidRoutes++;
                    }
                    
                } catch (ParseException e) {
                    System.out.println("路线 " + route.getRouteName() + " 凸包解析异常: " + e.getMessage());
                    invalidRoutes++;
                } catch (Exception e) {
                    System.out.println("路线 " + route.getRouteName() + " 处理异常: " + e.getMessage());
                    invalidRoutes++;
                }
            }
            
            System.out.println("\n路线处理统计:");
            System.out.println("有效路线: " + validRoutes);
            System.out.println("无效路线: " + invalidRoutes);
            System.out.println("polygonsGroup大小: " + polygonsGroup.size());
            
            // 4. 获取边界数据
            String boundaryStr = BoundaryPoint.getShaoguanBoundary();
            Readboundary readboundary = new Readboundary();
            Polygon boundary = readboundary.createPolygonFromLine(boundaryStr);
            
            System.out.println("\n边界数据:");
            System.out.println("边界是否存在: " + (boundary != null));
            if (boundary != null) {
                System.out.println("边界坐标数: " + boundary.getCoordinates().length);
                System.out.println("边界面积: " + boundary.getArea());
                System.out.println("边界是否有效: " + boundary.isValid());
            }
            
            // 5. 调用Networkfulling.solve方法
            if (!polygonsGroup.isEmpty() && boundary != null) {
                System.out.println("\n=== 调用Networkfulling.solve方法 ===");
                System.out.println("输入参数:");
                System.out.println("- polygonsGroup大小: " + polygonsGroup.size());
                System.out.println("- boundary: 存在");
                
                // 统计每个班组的路线数量
                Map<Integer, Integer> groupRouteCount = new HashMap<>();
                for (Integer groupId : polygonsGroup.values()) {
                    groupRouteCount.put(groupId, groupRouteCount.getOrDefault(groupId, 0) + 1);
                }
                System.out.println("班组路线分布:");
                for (Map.Entry<Integer, Integer> entry : groupRouteCount.entrySet()) {
                    System.out.println("  班组 " + entry.getKey() + ": " + entry.getValue() + " 条路线");
                }
                
                long startTime = System.currentTimeMillis();
                Map<Integer, List<Geometry>> result = Networkfulling.solve(polygonsGroup, boundary);
                long endTime = System.currentTimeMillis();
                
                System.out.println("\n执行结果:");
                System.out.println("执行时间: " + (endTime - startTime) + " ms");
                
                if (result != null) {
                    System.out.println("✅ 执行成功，返回班组数量: " + result.size());
                    
                    for (Map.Entry<Integer, List<Geometry>> entry : result.entrySet()) {
                        Integer groupId = entry.getKey();
                        List<Geometry> geometries = entry.getValue();
                        System.out.println("班组 " + groupId + ": " + geometries.size() + " 个几何对象");
                        
                        if (geometries.isEmpty()) {
                            System.out.println("  ⚠️ 班组 " + groupId + " 的几何对象列表为空！");
                        } else {
                            // 分析几何对象类型
                            Map<String, Integer> typeCount = new HashMap<>();
                            int totalCoords = 0;
                            for (Geometry geom : geometries) {
                                String type = geom.getGeometryType();
                                typeCount.put(type, typeCount.getOrDefault(type, 0) + 1);
                                totalCoords += geom.getCoordinates().length;
                            }
                            System.out.println("  几何类型分布: " + typeCount);
                            System.out.println("  总坐标数: " + totalCoords);
                        }
                    }
                    
                    // 检查是否所有班组都有结果
                    Set<Integer> inputGroups = new HashSet<>(polygonsGroup.values());
                    Set<Integer> outputGroups = result.keySet();
                    
                    if (inputGroups.equals(outputGroups)) {
                        System.out.println("✅ 所有输入班组都有对应的输出结果");
                    } else {
                        System.out.println("⚠️ 输入输出班组不匹配:");
                        System.out.println("  输入班组: " + inputGroups);
                        System.out.println("  输出班组: " + outputGroups);
                        
                        Set<Integer> missingGroups = new HashSet<>(inputGroups);
                        missingGroups.removeAll(outputGroups);
                        if (!missingGroups.isEmpty()) {
                            System.out.println("  缺失班组: " + missingGroups);
                        }
                    }
                    
                } else {
                    System.out.println("❌ 执行失败，返回null");
                }
                
            } else {
                System.out.println("❌ 无法执行测试：polygonsGroup为空或boundary为null");
            }
            
        } catch (Exception e) {
            System.out.println("❌ 测试执行异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
