-- 检查色块数据生成所需的基础数据
-- 1. 检查路线数据
SELECT 
    COUNT(*) as total_routes,
    COUNT(CASE WHEN is_delete = 0 THEN 1 END) as active_routes,
    COUNT(CASE WHEN is_delete = 0 AND convex IS NOT NULL AND convex != '' THEN 1 END) as routes_with_convex
FROM route;

-- 2. 检查班组数据
SELECT 
    group_id,
    group_name,
    colour
FROM `group` 
WHERE group_name LIKE '%班组%'
ORDER BY group_id;

-- 3. 检查每个班组的路线数量
SELECT 
    g.group_id,
    g.group_name,
    COUNT(r.route_id) as route_count,
    COUNT(CASE WHEN r.convex IS NOT NULL AND r.convex != '' THEN 1 END) as routes_with_convex
FROM `group` g
LEFT JOIN transit_depot td ON g.group_id = td.group_id
LEFT JOIN route r ON td.transit_depot_id = r.transit_depot_id AND r.is_delete = 0
WHERE g.group_name LIKE '%班组%'
GROUP BY g.group_id, g.group_name
ORDER BY g.group_id;

-- 4. 查看具体的路线凸包数据示例
SELECT 
    r.route_id,
    r.route_name,
    g.group_name,
    SUBSTRING(r.convex, 1, 100) as convex_sample
FROM route r
JOIN transit_depot td ON r.transit_depot_id = td.transit_depot_id
JOIN `group` g ON td.group_id = g.group_id
WHERE r.is_delete = 0 
    AND r.convex IS NOT NULL 
    AND r.convex != ''
    AND g.group_name LIKE '%班组%'
LIMIT 10;

-- 5. 检查中转站数据
SELECT 
    td.transit_depot_id,
    td.transit_depot_name,
    g.group_name,
    td.status
FROM transit_depot td
JOIN `group` g ON td.group_id = g.group_id
WHERE g.group_name LIKE '%班组%'
    AND td.status = 1
ORDER BY g.group_id;
