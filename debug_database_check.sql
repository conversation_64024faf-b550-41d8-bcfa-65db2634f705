-- 粤北卷烟物流管理系统数据库验证脚本
-- 用于检查色块显示问题的相关数据

-- 1. 检查route表中的凸包数据
SELECT 
    route_id,
    route_name,
    transit_depot_id,
    area_id,
    CASE 
        WHEN convex IS NULL THEN 'NULL'
        WHEN convex = '' THEN 'EMPTY'
        WHEN LENGTH(convex) < 50 THEN 'TOO_SHORT'
        ELSE 'VALID'
    END as convex_status,
    LENGTH(convex) as convex_length,
    LEFT(convex, 100) as convex_sample,
    is_delete
FROM route 
WHERE is_delete = 0
ORDER BY route_id
LIMIT 10;

-- 2. 统计route表中凸包数据的情况
SELECT 
    CASE 
        WHEN convex IS NULL THEN 'NULL'
        WHEN convex = '' THEN 'EMPTY'
        WHEN LENGTH(convex) < 50 THEN 'TOO_SHORT'
        ELSE 'VALID'
    END as convex_status,
    COUNT(*) as count
FROM route 
WHERE is_delete = 0
GROUP BY convex_status;

-- 3. 检查transit_depot表的数据
SELECT 
    transit_depot_id,
    transit_depot_name,
    group_id,
    status,
    is_delete
FROM transit_depot 
WHERE status = 1 AND is_delete = 0
ORDER BY transit_depot_id;

-- 4. 检查group表的数据
SELECT 
    group_id,
    group_name,
    colour,
    area_id
FROM `group` 
WHERE group_name LIKE '%班组%'
ORDER BY group_id;

-- 5. 检查route、transit_depot、group三表的关联关系
SELECT 
    r.route_id,
    r.route_name,
    r.transit_depot_id,
    td.transit_depot_name,
    td.group_id,
    g.group_name,
    g.colour,
    CASE 
        WHEN r.convex IS NULL THEN 'NULL'
        WHEN r.convex = '' THEN 'EMPTY'
        WHEN LENGTH(r.convex) < 50 THEN 'TOO_SHORT'
        ELSE 'VALID'
    END as convex_status
FROM route r
LEFT JOIN transit_depot td ON r.transit_depot_id = td.transit_depot_id
LEFT JOIN `group` g ON td.group_id = g.group_id
WHERE r.is_delete = 0 
    AND (td.status IS NULL OR td.status = 1)
    AND (td.is_delete IS NULL OR td.is_delete = 0)
ORDER BY g.group_id, r.route_id
LIMIT 20;

-- 6. 统计每个班组的路线数量
SELECT 
    g.group_id,
    g.group_name,
    g.colour,
    COUNT(r.route_id) as route_count,
    SUM(CASE WHEN r.convex IS NOT NULL AND r.convex != '' AND LENGTH(r.convex) >= 50 THEN 1 ELSE 0 END) as valid_convex_count
FROM `group` g
LEFT JOIN transit_depot td ON g.group_id = td.group_id
LEFT JOIN route r ON td.transit_depot_id = r.transit_depot_id
WHERE g.group_name LIKE '%班组%'
    AND (td.status IS NULL OR td.status = 1)
    AND (td.is_delete IS NULL OR td.is_delete = 0)
    AND (r.is_delete IS NULL OR r.is_delete = 0)
GROUP BY g.group_id, g.group_name, g.colour
ORDER BY g.group_id;

-- 7. 检查具体的凸包数据格式
SELECT 
    route_id,
    route_name,
    convex
FROM route 
WHERE is_delete = 0 
    AND convex IS NOT NULL 
    AND convex != ''
    AND LENGTH(convex) >= 50
LIMIT 3;
