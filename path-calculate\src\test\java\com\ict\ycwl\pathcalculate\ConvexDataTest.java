package com.ict.ycwl.pathcalculate;

import com.ict.ycwl.pathcalculate.utils.getColorUtils.Readroute;
import com.ict.ycwl.pathcalculate.utils.getColorUtils.Readboundary;
import com.ict.ycwl.pathcalculate.utils.getColorUtils.Networkfulling;
import com.ict.ycwl.pathcalculate.utils.BoundaryPoint;
import org.junit.jupiter.api.Test;
import org.locationtech.jts.geom.Polygon;
import org.locationtech.jts.geom.Geometry;
import org.locationtech.jts.io.ParseException;

import java.io.IOException;
import java.util.*;

/**
 * 凸包数据解析测试
 */
public class ConvexDataTest {

    @Test
    public void testConvexDataParsing() {
        System.out.println("=== 凸包数据解析测试 ===");
        
        // 测试数据：从数据库中获取的实际凸包数据
        String convexData = "114.117483,23.961694;113.822739,24.000398;113.801394,24.044648;114.149023,24.011932;114.14478,23.974555;114.117483,23.961694";
        
        Readroute readroute = new Readroute();
        
        try {
            System.out.println("原始凸包数据: " + convexData);
            
            Polygon polygon = readroute.createPolygonFromLine(convexData);
            
            if (polygon != null) {
                System.out.println("✅ 凸包解析成功");
                System.out.println("多边形: " + polygon.toText());
                System.out.println("坐标点数量: " + polygon.getCoordinates().length);
                System.out.println("面积: " + polygon.getArea());
                System.out.println("是否有效: " + polygon.isValid());
            } else {
                System.out.println("❌ 凸包解析失败，返回null");
            }
            
        } catch (ParseException e) {
            System.out.println("❌ 凸包解析异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    @Test
    public void testBoundaryDataParsing() {
        System.out.println("\n=== 边界数据解析测试 ===");
        
        try {
            String boundaryData = BoundaryPoint.getShaoguanBoundary();
            System.out.println("边界数据长度: " + boundaryData.length());
            System.out.println("边界数据前100字符: " + boundaryData.substring(0, Math.min(100, boundaryData.length())));
            
            Readboundary readboundary = new Readboundary();
            Polygon boundary = readboundary.createPolygonFromLine(boundaryData);
            
            if (boundary != null) {
                System.out.println("✅ 边界解析成功");
                System.out.println("边界坐标点数量: " + boundary.getCoordinates().length);
                System.out.println("边界面积: " + boundary.getArea());
                System.out.println("边界是否有效: " + boundary.isValid());
            } else {
                System.out.println("❌ 边界解析失败，返回null");
            }
            
        } catch (ParseException e) {
            System.out.println("❌ 边界解析异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    @Test
    public void testNetworkfullingSolve() {
        System.out.println("\n=== Networkfulling.solve方法测试 ===");
        
        try {
            // 创建测试数据
            Map<Polygon, Integer> polygonsGroup = new HashMap<>();
            Readroute readroute = new Readroute();
            
            // 添加几个测试凸包
            String[] testConvexData = {
                "114.117483,23.961694;113.822739,24.000398;113.801394,24.044648;114.149023,24.011932;114.14478,23.974555;114.117483,23.961694",
                "113.5,24.0;113.6,24.0;113.6,24.1;113.5,24.1;113.5,24.0",
                "114.0,24.2;114.1,24.2;114.1,24.3;114.0,24.3;114.0,24.2"
            };
            
            for (int i = 0; i < testConvexData.length; i++) {
                Polygon polygon = readroute.createPolygonFromLine(testConvexData[i]);
                if (polygon != null) {
                    polygonsGroup.put(polygon, i + 1); // 班组ID从1开始
                    System.out.println("添加测试凸包 " + (i + 1) + ": " + polygon.toText().substring(0, Math.min(50, polygon.toText().length())));
                }
            }
            
            // 创建边界
            String boundaryData = BoundaryPoint.getShaoguanBoundary();
            Readboundary readboundary = new Readboundary();
            Polygon boundary = readboundary.createPolygonFromLine(boundaryData);
            
            System.out.println("\n输入参数:");
            System.out.println("- polygonsGroup大小: " + polygonsGroup.size());
            System.out.println("- boundary: " + (boundary != null ? "存在" : "null"));
            
            // 调用Networkfulling.solve方法
            Map<Integer, List<Geometry>> result = Networkfulling.solve(polygonsGroup, boundary);
            
            System.out.println("\n结果分析:");
            if (result != null) {
                System.out.println("✅ Networkfulling.solve执行成功");
                System.out.println("返回班组数量: " + result.size());
                
                for (Map.Entry<Integer, List<Geometry>> entry : result.entrySet()) {
                    Integer groupId = entry.getKey();
                    List<Geometry> geometries = entry.getValue();
                    System.out.println("班组 " + groupId + ": " + geometries.size() + " 个几何对象");
                    
                    if (geometries.isEmpty()) {
                        System.out.println("  ⚠️ 班组 " + groupId + " 的几何对象列表为空");
                    } else {
                        for (int i = 0; i < Math.min(2, geometries.size()); i++) {
                            Geometry geom = geometries.get(i);
                            System.out.println("  几何对象 " + (i + 1) + ": " + geom.getGeometryType() + 
                                             ", 坐标数: " + geom.getCoordinates().length);
                        }
                    }
                }
            } else {
                System.out.println("❌ Networkfulling.solve返回null");
            }
            
        } catch (IOException e) {
            System.out.println("❌ Networkfulling.solve执行异常: " + e.getMessage());
            e.printStackTrace();
        } catch (ParseException e) {
            System.out.println("❌ 数据解析异常: " + e.getMessage());
            e.printStackTrace();
        } catch (Exception e) {
            System.out.println("❌ 未知异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    @Test
    public void testMultipleConvexData() {
        System.out.println("\n=== 多个凸包数据解析测试 ===");
        
        // 模拟数据库中的多个凸包数据
        String[] convexDataArray = {
            "114.117483,23.961694;113.822739,24.000398;113.801394,24.044648;114.149023,24.011932;114.14478,23.974555;114.117483,23.961694",
            "113.5,24.0;113.6,24.0;113.6,24.1;113.5,24.1;113.5,24.0",
            "114.0,24.2;114.1,24.2;114.1,24.3;114.0,24.3;114.0,24.2",
            "113.8,23.9;113.9,23.9;113.9,24.0;113.8,24.0;113.8,23.9",
            "114.2,24.1;114.3,24.1;114.3,24.2;114.2,24.2;114.2,24.1"
        };
        
        Readroute readroute = new Readroute();
        Map<Polygon, Integer> polygonsGroup = new HashMap<>();
        
        int successCount = 0;
        int failCount = 0;
        
        for (int i = 0; i < convexDataArray.length; i++) {
            try {
                String convexData = convexDataArray[i];
                Polygon polygon = readroute.createPolygonFromLine(convexData);
                
                if (polygon != null && polygon.isValid()) {
                    polygonsGroup.put(polygon, i + 1);
                    successCount++;
                    System.out.println("✅ 凸包 " + (i + 1) + " 解析成功");
                } else {
                    failCount++;
                    System.out.println("❌ 凸包 " + (i + 1) + " 解析失败或无效");
                }
                
            } catch (ParseException e) {
                failCount++;
                System.out.println("❌ 凸包 " + (i + 1) + " 解析异常: " + e.getMessage());
            }
        }
        
        System.out.println("\n解析结果统计:");
        System.out.println("成功: " + successCount + " 个");
        System.out.println("失败: " + failCount + " 个");
        System.out.println("最终polygonsGroup大小: " + polygonsGroup.size());
    }
}
