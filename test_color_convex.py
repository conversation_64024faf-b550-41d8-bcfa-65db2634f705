#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试粤北卷烟物流管理系统色块数据接口
"""

import requests
import json
import time

def test_get_all_colour_convex():
    """测试获取所有色块数据接口"""
    
    # 接口URL
    url = "http://localhost:8083/pathcalculate/path/getAllColourConvex"
    
    print("正在调用接口:", url)
    print("=" * 60)
    
    try:
        # 发送GET请求
        response = requests.get(url, timeout=30)
        
        print(f"HTTP状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        print("=" * 60)
        
        if response.status_code == 200:
            # 解析JSON响应
            data = response.json()
            print("响应数据:")
            print(json.dumps(data, indent=2, ensure_ascii=False))
            
            # 分析响应数据
            if isinstance(data, dict):
                if 'code' in data and 'data' in data:
                    code = data['code']
                    result_data = data['data']
                    msg = data.get('msg', '')
                    
                    print("=" * 60)
                    print(f"响应码: {code}")
                    print(f"响应消息: {msg}")
                    print(f"数据类型: {type(result_data)}")
                    
                    if isinstance(result_data, list):
                        print(f"数据长度: {len(result_data)}")
                        for i, group_data in enumerate(result_data):
                            if isinstance(group_data, list):
                                print(f"班组 {i+1}: {len(group_data)} 个色块")
                                if group_data:
                                    print(f"  第一个色块示例: {group_data[0][:100]}...")
                                else:
                                    print("  无色块数据")
                            else:
                                print(f"班组 {i+1}: 数据格式异常 - {type(group_data)}")
                    else:
                        print(f"数据格式异常: {type(result_data)}")
                else:
                    print("响应格式异常，缺少code或data字段")
            else:
                print(f"响应格式异常: {type(data)}")
                
        else:
            print(f"请求失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("连接失败：请确保path-calculate服务正在运行在端口8083")
    except requests.exceptions.Timeout:
        print("请求超时：接口响应时间过长")
    except requests.exceptions.RequestException as e:
        print(f"请求异常: {e}")
    except json.JSONDecodeError as e:
        print(f"JSON解析失败: {e}")
        print(f"原始响应: {response.text}")
    except Exception as e:
        print(f"未知错误: {e}")

def check_service_status():
    """检查服务状态"""
    try:
        response = requests.get("http://localhost:8083/actuator/health", timeout=5)
        if response.status_code == 200:
            print("✅ path-calculate服务正在运行")
            return True
        else:
            print(f"❌ 服务状态异常: {response.status_code}")
            return False
    except:
        print("❌ path-calculate服务未启动或无法访问")
        return False

if __name__ == "__main__":
    print("粤北卷烟物流管理系统 - 色块数据接口测试")
    print("=" * 60)
    
    # 检查服务状态
    if check_service_status():
        print()
        test_get_all_colour_convex()
    else:
        print("\n请先启动path-calculate服务:")
        print("cd path-calculate && mvn spring-boot:run")
