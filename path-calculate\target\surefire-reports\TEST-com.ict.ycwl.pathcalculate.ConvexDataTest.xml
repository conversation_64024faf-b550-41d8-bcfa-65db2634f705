<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report.xsd" name="com.ict.ycwl.pathcalculate.ConvexDataTest" time="0.458" tests="4" errors="0" skipped="0" failures="0">
  <properties>
    <property name="sun.desktop" value="windows"/>
    <property name="awt.toolkit" value="sun.awt.windows.WToolkit"/>
    <property name="file.encoding.pkg" value="sun.io"/>
    <property name="java.specification.version" value="1.8"/>
    <property name="sun.cpu.isalist" value="amd64"/>
    <property name="sun.jnu.encoding" value="GBK"/>
    <property name="java.class.path" value="D:\烟草\交接信息\源代码\ycwl-ms-v3.0\path-calculate\target\test-classes;D:\烟草\交接信息\源代码\ycwl-ms-v3.0\path-calculate\target\classes;D:\repository\org\springframework\boot\spring-boot-starter-web\2.3.9.RELEASE\spring-boot-starter-web-2.3.9.RELEASE.jar;D:\repository\org\springframework\boot\spring-boot-starter\2.3.9.RELEASE\spring-boot-starter-2.3.9.RELEASE.jar;D:\repository\org\springframework\boot\spring-boot\2.3.9.RELEASE\spring-boot-2.3.9.RELEASE.jar;D:\repository\org\springframework\boot\spring-boot-starter-logging\2.3.9.RELEASE\spring-boot-starter-logging-2.3.9.RELEASE.jar;D:\repository\ch\qos\logback\logback-classic\1.2.3\logback-classic-1.2.3.jar;D:\repository\ch\qos\logback\logback-core\1.2.3\logback-core-1.2.3.jar;D:\repository\org\apache\logging\log4j\log4j-to-slf4j\2.13.3\log4j-to-slf4j-2.13.3.jar;D:\repository\org\slf4j\jul-to-slf4j\1.7.30\jul-to-slf4j-1.7.30.jar;D:\repository\jakarta\annotation\jakarta.annotation-api\1.3.5\jakarta.annotation-api-1.3.5.jar;D:\repository\org\yaml\snakeyaml\1.26\snakeyaml-1.26.jar;D:\repository\org\springframework\boot\spring-boot-starter-json\2.3.9.RELEASE\spring-boot-starter-json-2.3.9.RELEASE.jar;D:\repository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.11.4\jackson-datatype-jdk8-2.11.4.jar;D:\repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.11.4\jackson-datatype-jsr310-2.11.4.jar;D:\repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.11.4\jackson-module-parameter-names-2.11.4.jar;D:\repository\org\springframework\boot\spring-boot-starter-tomcat\2.3.9.RELEASE\spring-boot-starter-tomcat-2.3.9.RELEASE.jar;D:\repository\org\apache\tomcat\embed\tomcat-embed-core\9.0.43\tomcat-embed-core-9.0.43.jar;D:\repository\org\apache\tomcat\embed\tomcat-embed-websocket\9.0.43\tomcat-embed-websocket-9.0.43.jar;D:\repository\org\springframework\spring-web\5.2.13.RELEASE\spring-web-5.2.13.RELEASE.jar;D:\repository\org\springframework\spring-beans\5.2.13.RELEASE\spring-beans-5.2.13.RELEASE.jar;D:\repository\org\springframework\spring-webmvc\5.2.13.RELEASE\spring-webmvc-5.2.13.RELEASE.jar;D:\repository\org\springframework\spring-aop\5.2.13.RELEASE\spring-aop-5.2.13.RELEASE.jar;D:\repository\org\springframework\spring-context\5.2.13.RELEASE\spring-context-5.2.13.RELEASE.jar;D:\repository\org\springframework\spring-expression\5.2.13.RELEASE\spring-expression-5.2.13.RELEASE.jar;D:\repository\org\springframework\boot\spring-boot-starter-test\2.3.9.RELEASE\spring-boot-starter-test-2.3.9.RELEASE.jar;D:\repository\org\springframework\boot\spring-boot-test\2.3.9.RELEASE\spring-boot-test-2.3.9.RELEASE.jar;D:\repository\org\springframework\boot\spring-boot-test-autoconfigure\2.3.9.RELEASE\spring-boot-test-autoconfigure-2.3.9.RELEASE.jar;D:\repository\com\jayway\jsonpath\json-path\2.4.0\json-path-2.4.0.jar;D:\repository\net\minidev\json-smart\2.3\json-smart-2.3.jar;D:\repository\net\minidev\accessors-smart\1.2\accessors-smart-1.2.jar;D:\repository\org\ow2\asm\asm\5.0.4\asm-5.0.4.jar;D:\repository\jakarta\xml\bind\jakarta.xml.bind-api\2.3.3\jakarta.xml.bind-api-2.3.3.jar;D:\repository\jakarta\activation\jakarta.activation-api\1.2.2\jakarta.activation-api-1.2.2.jar;D:\repository\org\assertj\assertj-core\3.16.1\assertj-core-3.16.1.jar;D:\repository\org\hamcrest\hamcrest\2.2\hamcrest-2.2.jar;D:\repository\org\junit\jupiter\junit-jupiter\5.6.3\junit-jupiter-5.6.3.jar;D:\repository\org\junit\jupiter\junit-jupiter-api\5.6.3\junit-jupiter-api-5.6.3.jar;D:\repository\org\opentest4j\opentest4j\1.2.0\opentest4j-1.2.0.jar;D:\repository\org\junit\platform\junit-platform-commons\1.6.3\junit-platform-commons-1.6.3.jar;D:\repository\org\junit\jupiter\junit-jupiter-params\5.6.3\junit-jupiter-params-5.6.3.jar;D:\repository\org\junit\jupiter\junit-jupiter-engine\5.6.3\junit-jupiter-engine-5.6.3.jar;D:\repository\org\junit\vintage\junit-vintage-engine\5.6.3\junit-vintage-engine-5.6.3.jar;D:\repository\org\apiguardian\apiguardian-api\1.1.0\apiguardian-api-1.1.0.jar;D:\repository\org\junit\platform\junit-platform-engine\1.6.3\junit-platform-engine-1.6.3.jar;D:\repository\org\mockito\mockito-core\3.3.3\mockito-core-3.3.3.jar;D:\repository\net\bytebuddy\byte-buddy\1.10.20\byte-buddy-1.10.20.jar;D:\repository\net\bytebuddy\byte-buddy-agent\1.10.20\byte-buddy-agent-1.10.20.jar;D:\repository\org\objenesis\objenesis\2.6\objenesis-2.6.jar;D:\repository\org\mockito\mockito-junit-jupiter\3.3.3\mockito-junit-jupiter-3.3.3.jar;D:\repository\org\skyscreamer\jsonassert\1.5.0\jsonassert-1.5.0.jar;D:\repository\com\vaadin\external\google\android-json\0.0.20131108.vaadin1\android-json-0.0.20131108.vaadin1.jar;D:\repository\org\springframework\spring-core\5.2.13.RELEASE\spring-core-5.2.13.RELEASE.jar;D:\repository\org\springframework\spring-jcl\5.2.13.RELEASE\spring-jcl-5.2.13.RELEASE.jar;D:\repository\org\springframework\spring-test\5.2.13.RELEASE\spring-test-5.2.13.RELEASE.jar;D:\repository\org\xmlunit\xmlunit-core\2.7.0\xmlunit-core-2.7.0.jar;D:\repository\junit\junit\4.13.2\junit-4.13.2.jar;D:\repository\org\hamcrest\hamcrest-core\2.2\hamcrest-core-2.2.jar;D:\repository\org\apache\httpcomponents\httpclient\4.5.13\httpclient-4.5.13.jar;D:\repository\org\apache\httpcomponents\httpcore\4.4.14\httpcore-4.4.14.jar;D:\repository\commons-codec\commons-codec\1.14\commons-codec-1.14.jar;D:\repository\com\baomidou\dynamic-datasource-spring-boot-starter\4.3.0\dynamic-datasource-spring-boot-starter-4.3.0.jar;D:\repository\com\baomidou\dynamic-datasource-spring-boot-common\4.3.0\dynamic-datasource-spring-boot-common-4.3.0.jar;D:\repository\com\baomidou\dynamic-datasource-spring\4.3.0\dynamic-datasource-spring-4.3.0.jar;D:\repository\com\baomidou\dynamic-datasource-creator\4.3.0\dynamic-datasource-creator-4.3.0.jar;D:\repository\org\springframework\boot\spring-boot-starter-aop\2.3.9.RELEASE\spring-boot-starter-aop-2.3.9.RELEASE.jar;D:\repository\org\aspectj\aspectjweaver\1.9.6\aspectjweaver-1.9.6.jar;D:\repository\com\alibaba\cloud\spring-cloud-starter-alibaba-nacos-discovery\2.2.6.RELEASE\spring-cloud-starter-alibaba-nacos-discovery-2.2.6.RELEASE.jar;D:\repository\com\alibaba\cloud\spring-cloud-alibaba-commons\2.2.6.RELEASE\spring-cloud-alibaba-commons-2.2.6.RELEASE.jar;D:\repository\com\alibaba\nacos\nacos-client\1.4.2\nacos-client-1.4.2.jar;D:\repository\com\alibaba\nacos\nacos-common\1.4.2\nacos-common-1.4.2.jar;D:\repository\org\apache\httpcomponents\httpasyncclient\4.1.4\httpasyncclient-4.1.4.jar;D:\repository\org\apache\httpcomponents\httpcore-nio\4.4.14\httpcore-nio-4.4.14.jar;D:\repository\com\alibaba\nacos\nacos-api\1.4.2\nacos-api-1.4.2.jar;D:\repository\io\prometheus\simpleclient\0.5.0\simpleclient-0.5.0.jar;D:\repository\com\alibaba\spring\spring-context-support\1.0.10\spring-context-support-1.0.10.jar;D:\repository\org\springframework\cloud\spring-cloud-commons\2.2.7.RELEASE\spring-cloud-commons-2.2.7.RELEASE.jar;D:\repository\org\springframework\security\spring-security-crypto\5.3.8.RELEASE\spring-security-crypto-5.3.8.RELEASE.jar;D:\repository\org\springframework\cloud\spring-cloud-context\2.2.7.RELEASE\spring-cloud-context-2.2.7.RELEASE.jar;D:\repository\org\springframework\cloud\spring-cloud-starter-netflix-ribbon\2.2.7.RELEASE\spring-cloud-starter-netflix-ribbon-2.2.7.RELEASE.jar;D:\repository\org\springframework\cloud\spring-cloud-starter\2.2.7.RELEASE\spring-cloud-starter-2.2.7.RELEASE.jar;D:\repository\org\springframework\security\spring-security-rsa\1.0.9.RELEASE\spring-security-rsa-1.0.9.RELEASE.jar;D:\repository\org\bouncycastle\bcpkix-jdk15on\1.64\bcpkix-jdk15on-1.64.jar;D:\repository\org\bouncycastle\bcprov-jdk15on\1.64\bcprov-jdk15on-1.64.jar;D:\repository\org\springframework\cloud\spring-cloud-netflix-ribbon\2.2.7.RELEASE\spring-cloud-netflix-ribbon-2.2.7.RELEASE.jar;D:\repository\org\springframework\cloud\spring-cloud-netflix-archaius\2.2.7.RELEASE\spring-cloud-netflix-archaius-2.2.7.RELEASE.jar;D:\repository\org\springframework\cloud\spring-cloud-starter-netflix-archaius\2.2.7.RELEASE\spring-cloud-starter-netflix-archaius-2.2.7.RELEASE.jar;D:\repository\com\netflix\archaius\archaius-core\0.7.7\archaius-core-0.7.7.jar;D:\repository\commons-configuration\commons-configuration\1.8\commons-configuration-1.8.jar;D:\repository\com\netflix\ribbon\ribbon\2.3.0\ribbon-2.3.0.jar;D:\repository\com\netflix\ribbon\ribbon-transport\2.3.0\ribbon-transport-2.3.0.jar;D:\repository\io\reactivex\rxnetty-contexts\0.4.9\rxnetty-contexts-0.4.9.jar;D:\repository\io\reactivex\rxnetty-servo\0.4.9\rxnetty-servo-0.4.9.jar;D:\repository\com\netflix\hystrix\hystrix-core\1.5.18\hystrix-core-1.5.18.jar;D:\repository\org\hdrhistogram\HdrHistogram\2.1.9\HdrHistogram-2.1.9.jar;D:\repository\javax\inject\javax.inject\1\javax.inject-1.jar;D:\repository\io\reactivex\rxnetty\0.4.9\rxnetty-0.4.9.jar;D:\repository\com\netflix\ribbon\ribbon-core\2.3.0\ribbon-core-2.3.0.jar;D:\repository\commons-lang\commons-lang\2.6\commons-lang-2.6.jar;D:\repository\com\netflix\ribbon\ribbon-httpclient\2.3.0\ribbon-httpclient-2.3.0.jar;D:\repository\commons-collections\commons-collections\3.2.2\commons-collections-3.2.2.jar;D:\repository\com\sun\jersey\jersey-client\1.19.1\jersey-client-1.19.1.jar;D:\repository\com\sun\jersey\jersey-core\1.19.1\jersey-core-1.19.1.jar;D:\repository\javax\ws\rs\jsr311-api\1.1.1\jsr311-api-1.1.1.jar;D:\repository\com\sun\jersey\contribs\jersey-apache-client4\1.19.1\jersey-apache-client4-1.19.1.jar;D:\repository\com\netflix\servo\servo-core\0.12.21\servo-core-0.12.21.jar;D:\repository\com\netflix\netflix-commons\netflix-commons-util\0.3.0\netflix-commons-util-0.3.0.jar;D:\repository\com\netflix\ribbon\ribbon-loadbalancer\2.3.0\ribbon-loadbalancer-2.3.0.jar;D:\repository\com\netflix\netflix-commons\netflix-statistics\0.1.1\netflix-statistics-0.1.1.jar;D:\repository\io\reactivex\rxjava\1.3.8\rxjava-1.3.8.jar;D:\repository\com\alibaba\cloud\spring-cloud-starter-alibaba-nacos-config\2.2.6.RELEASE\spring-cloud-starter-alibaba-nacos-config-2.2.6.RELEASE.jar;D:\repository\mysql\mysql-connector-java\5.1.47\mysql-connector-java-5.1.47.jar;D:\repository\com\alibaba\druid-spring-boot-starter\1.2.25\druid-spring-boot-starter-1.2.25.jar;D:\repository\com\alibaba\druid\1.2.25\druid-1.2.25.jar;D:\repository\org\slf4j\slf4j-api\1.7.30\slf4j-api-1.7.30.jar;D:\repository\org\springframework\boot\spring-boot-autoconfigure\2.3.9.RELEASE\spring-boot-autoconfigure-2.3.9.RELEASE.jar;D:\repository\org\glassfish\jaxb\jaxb-runtime\2.3.3\jaxb-runtime-2.3.3.jar;D:\repository\org\glassfish\jaxb\txw2\2.3.3\txw2-2.3.3.jar;D:\repository\com\sun\istack\istack-commons-runtime\3.0.11\istack-commons-runtime-3.0.11.jar;D:\repository\com\sun\activation\jakarta.activation\1.2.2\jakarta.activation-1.2.2.jar;D:\repository\org\mybatis\spring\boot\mybatis-spring-boot-starter\2.1.1\mybatis-spring-boot-starter-2.1.1.jar;D:\repository\org\springframework\boot\spring-boot-starter-jdbc\2.3.9.RELEASE\spring-boot-starter-jdbc-2.3.9.RELEASE.jar;D:\repository\com\zaxxer\HikariCP\3.4.5\HikariCP-3.4.5.jar;D:\repository\org\springframework\spring-jdbc\5.2.13.RELEASE\spring-jdbc-5.2.13.RELEASE.jar;D:\repository\org\springframework\spring-tx\5.2.13.RELEASE\spring-tx-5.2.13.RELEASE.jar;D:\repository\org\mybatis\spring\boot\mybatis-spring-boot-autoconfigure\2.1.1\mybatis-spring-boot-autoconfigure-2.1.1.jar;D:\repository\org\mybatis\mybatis\3.5.3\mybatis-3.5.3.jar;D:\repository\org\mybatis\mybatis-spring\2.0.3\mybatis-spring-2.0.3.jar;D:\repository\org\projectlombok\lombok\1.18.18\lombok-1.18.18.jar;D:\repository\io\springfox\springfox-swagger2\2.9.2\springfox-swagger2-2.9.2.jar;D:\repository\io\swagger\swagger-annotations\1.5.20\swagger-annotations-1.5.20.jar;D:\repository\io\swagger\swagger-models\1.5.20\swagger-models-1.5.20.jar;D:\repository\io\springfox\springfox-spi\2.9.2\springfox-spi-2.9.2.jar;D:\repository\io\springfox\springfox-core\2.9.2\springfox-core-2.9.2.jar;D:\repository\io\springfox\springfox-schema\2.9.2\springfox-schema-2.9.2.jar;D:\repository\io\springfox\springfox-swagger-common\2.9.2\springfox-swagger-common-2.9.2.jar;D:\repository\io\springfox\springfox-spring-web\2.9.2\springfox-spring-web-2.9.2.jar;D:\repository\com\fasterxml\classmate\1.5.1\classmate-1.5.1.jar;D:\repository\org\springframework\plugin\spring-plugin-core\1.2.0.RELEASE\spring-plugin-core-1.2.0.RELEASE.jar;D:\repository\org\springframework\plugin\spring-plugin-metadata\1.2.0.RELEASE\spring-plugin-metadata-1.2.0.RELEASE.jar;D:\repository\org\mapstruct\mapstruct\1.2.0.Final\mapstruct-1.2.0.Final.jar;D:\repository\com\github\xiaoymin\knife4j-spring-ui\3.0.2\knife4j-spring-ui-3.0.2.jar;D:\repository\io\springfox\springfox-swagger-ui\2.9.2\springfox-swagger-ui-2.9.2.jar;D:\repository\cn\hutool\hutool-all\5.8.8\hutool-all-5.8.8.jar;D:\repository\com\ict\ycwl\common\1.0-SNAPSHOT\common-1.0-SNAPSHOT.jar;D:\repository\org\apache\commons\commons-lang3\3.10\commons-lang3-3.10.jar;D:\repository\com\github\axet\kaptcha\0.0.9\kaptcha-0.0.9.jar;D:\repository\com\jhlabs\filters\2.0.235\filters-2.0.235.jar;D:\repository\com\auth0\java-jwt\3.4.0\java-jwt-3.4.0.jar;D:\repository\org\springframework\boot\spring-boot-starter-validation\2.3.2.RELEASE\spring-boot-starter-validation-2.3.2.RELEASE.jar;D:\repository\org\glassfish\jakarta.el\3.0.3\jakarta.el-3.0.3.jar;D:\repository\org\hibernate\validator\hibernate-validator\6.1.7.Final\hibernate-validator-6.1.7.Final.jar;D:\repository\jakarta\validation\jakarta.validation-api\2.0.2\jakarta.validation-api-2.0.2.jar;D:\repository\org\jboss\logging\jboss-logging\3.4.1.Final\jboss-logging-3.4.1.Final.jar;D:\repository\com\github\pagehelper\pagehelper-spring-boot-starter\1.4.6\pagehelper-spring-boot-starter-1.4.6.jar;D:\repository\com\github\pagehelper\pagehelper-spring-boot-autoconfigure\1.4.6\pagehelper-spring-boot-autoconfigure-1.4.6.jar;D:\repository\com\github\pagehelper\pagehelper\5.3.2\pagehelper-5.3.2.jar;D:\repository\com\github\jsqlparser\jsqlparser\4.5\jsqlparser-4.5.jar;D:\repository\com\baomidou\mybatis-plus-boot-starter\3.5.1\mybatis-plus-boot-starter-3.5.1.jar;D:\repository\com\baomidou\mybatis-plus\3.5.1\mybatis-plus-3.5.1.jar;D:\repository\com\baomidou\mybatis-plus-extension\3.5.1\mybatis-plus-extension-3.5.1.jar;D:\repository\com\baomidou\mybatis-plus-core\3.5.1\mybatis-plus-core-3.5.1.jar;D:\repository\com\baomidou\mybatis-plus-annotation\3.5.1\mybatis-plus-annotation-3.5.1.jar;D:\repository\org\apache\poi\poi\5.2.3\poi-5.2.3.jar;D:\repository\org\apache\commons\commons-collections4\4.4\commons-collections4-4.4.jar;D:\repository\commons-io\commons-io\2.11.0\commons-io-2.11.0.jar;D:\repository\com\zaxxer\SparseBitSet\1.2\SparseBitSet-1.2.jar;D:\repository\org\apache\logging\log4j\log4j-api\2.13.3\log4j-api-2.13.3.jar;D:\repository\org\apache\poi\poi-ooxml\5.2.3\poi-ooxml-5.2.3.jar;D:\repository\org\apache\poi\poi-ooxml-lite\5.2.3\poi-ooxml-lite-5.2.3.jar;D:\repository\org\apache\xmlbeans\xmlbeans\5.1.1\xmlbeans-5.1.1.jar;D:\repository\xml-apis\xml-apis\1.4.01\xml-apis-1.4.01.jar;D:\repository\org\apache\commons\commons-compress\1.21\commons-compress-1.21.jar;D:\repository\com\github\virtuald\curvesapi\1.07\curvesapi-1.07.jar;D:\repository\org\locationtech\jts\jts-core\1.19.0\jts-core-1.19.0.jar;D:\repository\org\locationtech\jts\io\jts-io-common\1.19.0\jts-io-common-1.19.0.jar;D:\repository\com\googlecode\json-simple\json-simple\1.1.1\json-simple-1.1.1.jar;D:\repository\com\alibaba\easyexcel\3.1.1\easyexcel-3.1.1.jar;D:\repository\com\alibaba\easyexcel-core\3.1.1\easyexcel-core-3.1.1.jar;D:\repository\com\alibaba\easyexcel-support\3.1.1\easyexcel-support-3.1.1.jar;D:\repository\org\apache\poi\poi-ooxml-schemas\4.1.2\poi-ooxml-schemas-4.1.2.jar;D:\repository\org\apache\commons\commons-csv\1.8\commons-csv-1.8.jar;D:\repository\org\ehcache\ehcache\3.8.1\ehcache-3.8.1.jar;D:\repository\com\google\guava\guava\33.4.0-jre\guava-33.4.0-jre.jar;D:\repository\com\google\guava\failureaccess\1.0.2\failureaccess-1.0.2.jar;D:\repository\com\google\guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar;D:\repository\com\google\code\findbugs\jsr305\3.0.2\jsr305-3.0.2.jar;D:\repository\org\checkerframework\checker-qual\3.43.0\checker-qual-3.43.0.jar;D:\repository\com\google\errorprone\error_prone_annotations\2.36.0\error_prone_annotations-2.36.0.jar;D:\repository\com\google\j2objc\j2objc-annotations\3.0.0\j2objc-annotations-3.0.0.jar;D:\repository\com\google\ortools\ortools-java\9.8.3296\ortools-java-9.8.3296.jar;D:\repository\com\google\ortools\ortools-linux-x86-64\9.8.3296\ortools-linux-x86-64-9.8.3296.jar;D:\repository\com\google\ortools\ortools-darwin-x86-64\9.8.3296\ortools-darwin-x86-64-9.8.3296.jar;D:\repository\com\google\ortools\ortools-linux-aarch64\9.8.3296\ortools-linux-aarch64-9.8.3296.jar;D:\repository\com\google\ortools\ortools-darwin-aarch64\9.8.3296\ortools-darwin-aarch64-9.8.3296.jar;D:\repository\net\java\dev\jna\jna-platform\5.13.0\jna-platform-5.13.0.jar;D:\repository\net\java\dev\jna\jna\5.13.0\jna-5.13.0.jar;D:\repository\com\google\protobuf\protobuf-java\3.13.0\protobuf-java-3.13.0.jar;D:\repository\com\google\ortools\ortools-win32-x86-64\9.8.3296\ortools-win32-x86-64-9.8.3296.jar;D:\repository\org\apache\commons\commons-math3\3.6.1\commons-math3-3.6.1.jar;D:\repository\io\jenetics\jenetics\7.2.0\jenetics-7.2.0.jar;D:\repository\com\fasterxml\jackson\core\jackson-databind\2.15.2\jackson-databind-2.15.2.jar;D:\repository\com\fasterxml\jackson\core\jackson-core\2.15.2\jackson-core-2.15.2.jar;D:\repository\com\fasterxml\jackson\core\jackson-annotations\2.15.2\jackson-annotations-2.15.2.jar;D:\repository\com\h2database\h2\1.4.200\h2-1.4.200.jar;D:\repository\org\optaplanner\optaplanner-core\7.73.0.Final\optaplanner-core-7.73.0.Final.jar;D:\repository\org\kie\kie-api\7.73.0.Final\kie-api-7.73.0.Final.jar;D:\repository\org\kie\soup\kie-soup-maven-support\7.73.0.Final\kie-soup-maven-support-7.73.0.Final.jar;D:\repository\org\kie\kie-internal\7.73.0.Final\kie-internal-7.73.0.Final.jar;D:\repository\org\drools\drools-core\7.73.0.Final\drools-core-7.73.0.Final.jar;D:\repository\org\kie\soup\kie-soup-xstream\7.73.0.Final\kie-soup-xstream-7.73.0.Final.jar;D:\repository\org\drools\drools-core-reflective\7.73.0.Final\drools-core-reflective-7.73.0.Final.jar;D:\repository\org\drools\drools-core-dynamic\7.73.0.Final\drools-core-dynamic-7.73.0.Final.jar;D:\repository\org\drools\drools-compiler\7.73.0.Final\drools-compiler-7.73.0.Final.jar;D:\repository\org\kie\kie-memory-compiler\7.73.0.Final\kie-memory-compiler-7.73.0.Final.jar;D:\repository\org\drools\drools-ecj\7.73.0.Final\drools-ecj-7.73.0.Final.jar;D:\repository\org\antlr\antlr-runtime\3.5.2\antlr-runtime-3.5.2.jar;D:\repository\org\drools\drools-mvel\7.73.0.Final\drools-mvel-7.73.0.Final.jar;D:\repository\org\mvel\mvel2\2.4.14.Final\mvel2-2.4.14.Final.jar;D:\repository\org\kie\soup\kie-soup-project-datamodel-commons\7.73.0.Final\kie-soup-project-datamodel-commons-7.73.0.Final.jar;D:\repository\org\kie\soup\kie-soup-commons\7.73.0.Final\kie-soup-commons-7.73.0.Final.jar;D:\repository\org\kie\soup\kie-soup-project-datamodel-api\7.73.0.Final\kie-soup-project-datamodel-api-7.73.0.Final.jar;D:\repository\org\drools\drools-canonical-model\7.73.0.Final\drools-canonical-model-7.73.0.Final.jar;D:\repository\org\drools\drools-model-compiler\7.73.0.Final\drools-model-compiler-7.73.0.Final.jar;D:\repository\com\github\javaparser\javaparser-core\3.23.1\javaparser-core-3.23.1.jar;D:\repository\org\drools\drools-mvel-parser\7.73.0.Final\drools-mvel-parser-7.73.0.Final.jar;D:\repository\org\drools\drools-mvel-compiler\7.73.0.Final\drools-mvel-compiler-7.73.0.Final.jar;D:\repository\org\reflections\reflections\0.9.11\reflections-0.9.11.jar;D:\repository\org\javassist\javassist\3.21.0-GA\javassist-3.21.0-GA.jar;D:\repository\com\thoughtworks\xstream\xstream\1.4.19\xstream-1.4.19.jar;D:\repository\io\github\x-stream\mxparser\1.2.2\mxparser-1.2.2.jar;D:\repository\xmlpull\xmlpull\1.1.3.1\xmlpull-1.1.3.1.jar;D:\repository\org\optaplanner\optaplanner-spring-boot-starter\7.73.0.Final\optaplanner-spring-boot-starter-7.73.0.Final.jar;D:\repository\org\optaplanner\optaplanner-spring-boot-autoconfigure\7.73.0.Final\optaplanner-spring-boot-autoconfigure-7.73.0.Final.jar;D:\repository\org\optaplanner\optaplanner-persistence-jackson\7.73.0.Final\optaplanner-persistence-jackson-7.73.0.Final.jar;D:\repository\org\optaplanner\optaplanner-persistence-common\7.73.0.Final\optaplanner-persistence-common-7.73.0.Final.jar;D:\repository\com\graphhopper\jsprit-core\1.8\jsprit-core-1.8.jar;D:\repository\com\uber\h3\3.7.3\h3-3.7.3.jar;"/>
    <property name="java.vm.vendor" value="Oracle Corporation"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="user.variant" value=""/>
    <property name="java.vendor.url" value="http://java.oracle.com/"/>
    <property name="user.timezone" value="Asia/Shanghai"/>
    <property name="java.vm.specification.version" value="1.8"/>
    <property name="os.name" value="Windows 10"/>
    <property name="user.country" value="CN"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="sun.boot.library.path" value="C:\Program Files\Java\jdk1.8.0_151\jre\bin"/>
    <property name="sun.java.command" value="C:\Users\<USER>\AppData\Local\Temp\surefire311204213725103928\surefirebooter7759666411478501378.jar C:\Users\<USER>\AppData\Local\Temp\surefire311204213725103928 2025-08-20T02-32-35_739-jvmRun1 surefire2015884948152857056tmp surefire_07097134837453863525tmp"/>
    <property name="test" value="ConvexDataTest"/>
    <property name="surefire.test.class.path" value="D:\烟草\交接信息\源代码\ycwl-ms-v3.0\path-calculate\target\test-classes;D:\烟草\交接信息\源代码\ycwl-ms-v3.0\path-calculate\target\classes;D:\repository\org\springframework\boot\spring-boot-starter-web\2.3.9.RELEASE\spring-boot-starter-web-2.3.9.RELEASE.jar;D:\repository\org\springframework\boot\spring-boot-starter\2.3.9.RELEASE\spring-boot-starter-2.3.9.RELEASE.jar;D:\repository\org\springframework\boot\spring-boot\2.3.9.RELEASE\spring-boot-2.3.9.RELEASE.jar;D:\repository\org\springframework\boot\spring-boot-starter-logging\2.3.9.RELEASE\spring-boot-starter-logging-2.3.9.RELEASE.jar;D:\repository\ch\qos\logback\logback-classic\1.2.3\logback-classic-1.2.3.jar;D:\repository\ch\qos\logback\logback-core\1.2.3\logback-core-1.2.3.jar;D:\repository\org\apache\logging\log4j\log4j-to-slf4j\2.13.3\log4j-to-slf4j-2.13.3.jar;D:\repository\org\slf4j\jul-to-slf4j\1.7.30\jul-to-slf4j-1.7.30.jar;D:\repository\jakarta\annotation\jakarta.annotation-api\1.3.5\jakarta.annotation-api-1.3.5.jar;D:\repository\org\yaml\snakeyaml\1.26\snakeyaml-1.26.jar;D:\repository\org\springframework\boot\spring-boot-starter-json\2.3.9.RELEASE\spring-boot-starter-json-2.3.9.RELEASE.jar;D:\repository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.11.4\jackson-datatype-jdk8-2.11.4.jar;D:\repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.11.4\jackson-datatype-jsr310-2.11.4.jar;D:\repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.11.4\jackson-module-parameter-names-2.11.4.jar;D:\repository\org\springframework\boot\spring-boot-starter-tomcat\2.3.9.RELEASE\spring-boot-starter-tomcat-2.3.9.RELEASE.jar;D:\repository\org\apache\tomcat\embed\tomcat-embed-core\9.0.43\tomcat-embed-core-9.0.43.jar;D:\repository\org\apache\tomcat\embed\tomcat-embed-websocket\9.0.43\tomcat-embed-websocket-9.0.43.jar;D:\repository\org\springframework\spring-web\5.2.13.RELEASE\spring-web-5.2.13.RELEASE.jar;D:\repository\org\springframework\spring-beans\5.2.13.RELEASE\spring-beans-5.2.13.RELEASE.jar;D:\repository\org\springframework\spring-webmvc\5.2.13.RELEASE\spring-webmvc-5.2.13.RELEASE.jar;D:\repository\org\springframework\spring-aop\5.2.13.RELEASE\spring-aop-5.2.13.RELEASE.jar;D:\repository\org\springframework\spring-context\5.2.13.RELEASE\spring-context-5.2.13.RELEASE.jar;D:\repository\org\springframework\spring-expression\5.2.13.RELEASE\spring-expression-5.2.13.RELEASE.jar;D:\repository\org\springframework\boot\spring-boot-starter-test\2.3.9.RELEASE\spring-boot-starter-test-2.3.9.RELEASE.jar;D:\repository\org\springframework\boot\spring-boot-test\2.3.9.RELEASE\spring-boot-test-2.3.9.RELEASE.jar;D:\repository\org\springframework\boot\spring-boot-test-autoconfigure\2.3.9.RELEASE\spring-boot-test-autoconfigure-2.3.9.RELEASE.jar;D:\repository\com\jayway\jsonpath\json-path\2.4.0\json-path-2.4.0.jar;D:\repository\net\minidev\json-smart\2.3\json-smart-2.3.jar;D:\repository\net\minidev\accessors-smart\1.2\accessors-smart-1.2.jar;D:\repository\org\ow2\asm\asm\5.0.4\asm-5.0.4.jar;D:\repository\jakarta\xml\bind\jakarta.xml.bind-api\2.3.3\jakarta.xml.bind-api-2.3.3.jar;D:\repository\jakarta\activation\jakarta.activation-api\1.2.2\jakarta.activation-api-1.2.2.jar;D:\repository\org\assertj\assertj-core\3.16.1\assertj-core-3.16.1.jar;D:\repository\org\hamcrest\hamcrest\2.2\hamcrest-2.2.jar;D:\repository\org\junit\jupiter\junit-jupiter\5.6.3\junit-jupiter-5.6.3.jar;D:\repository\org\junit\jupiter\junit-jupiter-api\5.6.3\junit-jupiter-api-5.6.3.jar;D:\repository\org\opentest4j\opentest4j\1.2.0\opentest4j-1.2.0.jar;D:\repository\org\junit\platform\junit-platform-commons\1.6.3\junit-platform-commons-1.6.3.jar;D:\repository\org\junit\jupiter\junit-jupiter-params\5.6.3\junit-jupiter-params-5.6.3.jar;D:\repository\org\junit\jupiter\junit-jupiter-engine\5.6.3\junit-jupiter-engine-5.6.3.jar;D:\repository\org\junit\vintage\junit-vintage-engine\5.6.3\junit-vintage-engine-5.6.3.jar;D:\repository\org\apiguardian\apiguardian-api\1.1.0\apiguardian-api-1.1.0.jar;D:\repository\org\junit\platform\junit-platform-engine\1.6.3\junit-platform-engine-1.6.3.jar;D:\repository\org\mockito\mockito-core\3.3.3\mockito-core-3.3.3.jar;D:\repository\net\bytebuddy\byte-buddy\1.10.20\byte-buddy-1.10.20.jar;D:\repository\net\bytebuddy\byte-buddy-agent\1.10.20\byte-buddy-agent-1.10.20.jar;D:\repository\org\objenesis\objenesis\2.6\objenesis-2.6.jar;D:\repository\org\mockito\mockito-junit-jupiter\3.3.3\mockito-junit-jupiter-3.3.3.jar;D:\repository\org\skyscreamer\jsonassert\1.5.0\jsonassert-1.5.0.jar;D:\repository\com\vaadin\external\google\android-json\0.0.20131108.vaadin1\android-json-0.0.20131108.vaadin1.jar;D:\repository\org\springframework\spring-core\5.2.13.RELEASE\spring-core-5.2.13.RELEASE.jar;D:\repository\org\springframework\spring-jcl\5.2.13.RELEASE\spring-jcl-5.2.13.RELEASE.jar;D:\repository\org\springframework\spring-test\5.2.13.RELEASE\spring-test-5.2.13.RELEASE.jar;D:\repository\org\xmlunit\xmlunit-core\2.7.0\xmlunit-core-2.7.0.jar;D:\repository\junit\junit\4.13.2\junit-4.13.2.jar;D:\repository\org\hamcrest\hamcrest-core\2.2\hamcrest-core-2.2.jar;D:\repository\org\apache\httpcomponents\httpclient\4.5.13\httpclient-4.5.13.jar;D:\repository\org\apache\httpcomponents\httpcore\4.4.14\httpcore-4.4.14.jar;D:\repository\commons-codec\commons-codec\1.14\commons-codec-1.14.jar;D:\repository\com\baomidou\dynamic-datasource-spring-boot-starter\4.3.0\dynamic-datasource-spring-boot-starter-4.3.0.jar;D:\repository\com\baomidou\dynamic-datasource-spring-boot-common\4.3.0\dynamic-datasource-spring-boot-common-4.3.0.jar;D:\repository\com\baomidou\dynamic-datasource-spring\4.3.0\dynamic-datasource-spring-4.3.0.jar;D:\repository\com\baomidou\dynamic-datasource-creator\4.3.0\dynamic-datasource-creator-4.3.0.jar;D:\repository\org\springframework\boot\spring-boot-starter-aop\2.3.9.RELEASE\spring-boot-starter-aop-2.3.9.RELEASE.jar;D:\repository\org\aspectj\aspectjweaver\1.9.6\aspectjweaver-1.9.6.jar;D:\repository\com\alibaba\cloud\spring-cloud-starter-alibaba-nacos-discovery\2.2.6.RELEASE\spring-cloud-starter-alibaba-nacos-discovery-2.2.6.RELEASE.jar;D:\repository\com\alibaba\cloud\spring-cloud-alibaba-commons\2.2.6.RELEASE\spring-cloud-alibaba-commons-2.2.6.RELEASE.jar;D:\repository\com\alibaba\nacos\nacos-client\1.4.2\nacos-client-1.4.2.jar;D:\repository\com\alibaba\nacos\nacos-common\1.4.2\nacos-common-1.4.2.jar;D:\repository\org\apache\httpcomponents\httpasyncclient\4.1.4\httpasyncclient-4.1.4.jar;D:\repository\org\apache\httpcomponents\httpcore-nio\4.4.14\httpcore-nio-4.4.14.jar;D:\repository\com\alibaba\nacos\nacos-api\1.4.2\nacos-api-1.4.2.jar;D:\repository\io\prometheus\simpleclient\0.5.0\simpleclient-0.5.0.jar;D:\repository\com\alibaba\spring\spring-context-support\1.0.10\spring-context-support-1.0.10.jar;D:\repository\org\springframework\cloud\spring-cloud-commons\2.2.7.RELEASE\spring-cloud-commons-2.2.7.RELEASE.jar;D:\repository\org\springframework\security\spring-security-crypto\5.3.8.RELEASE\spring-security-crypto-5.3.8.RELEASE.jar;D:\repository\org\springframework\cloud\spring-cloud-context\2.2.7.RELEASE\spring-cloud-context-2.2.7.RELEASE.jar;D:\repository\org\springframework\cloud\spring-cloud-starter-netflix-ribbon\2.2.7.RELEASE\spring-cloud-starter-netflix-ribbon-2.2.7.RELEASE.jar;D:\repository\org\springframework\cloud\spring-cloud-starter\2.2.7.RELEASE\spring-cloud-starter-2.2.7.RELEASE.jar;D:\repository\org\springframework\security\spring-security-rsa\1.0.9.RELEASE\spring-security-rsa-1.0.9.RELEASE.jar;D:\repository\org\bouncycastle\bcpkix-jdk15on\1.64\bcpkix-jdk15on-1.64.jar;D:\repository\org\bouncycastle\bcprov-jdk15on\1.64\bcprov-jdk15on-1.64.jar;D:\repository\org\springframework\cloud\spring-cloud-netflix-ribbon\2.2.7.RELEASE\spring-cloud-netflix-ribbon-2.2.7.RELEASE.jar;D:\repository\org\springframework\cloud\spring-cloud-netflix-archaius\2.2.7.RELEASE\spring-cloud-netflix-archaius-2.2.7.RELEASE.jar;D:\repository\org\springframework\cloud\spring-cloud-starter-netflix-archaius\2.2.7.RELEASE\spring-cloud-starter-netflix-archaius-2.2.7.RELEASE.jar;D:\repository\com\netflix\archaius\archaius-core\0.7.7\archaius-core-0.7.7.jar;D:\repository\commons-configuration\commons-configuration\1.8\commons-configuration-1.8.jar;D:\repository\com\netflix\ribbon\ribbon\2.3.0\ribbon-2.3.0.jar;D:\repository\com\netflix\ribbon\ribbon-transport\2.3.0\ribbon-transport-2.3.0.jar;D:\repository\io\reactivex\rxnetty-contexts\0.4.9\rxnetty-contexts-0.4.9.jar;D:\repository\io\reactivex\rxnetty-servo\0.4.9\rxnetty-servo-0.4.9.jar;D:\repository\com\netflix\hystrix\hystrix-core\1.5.18\hystrix-core-1.5.18.jar;D:\repository\org\hdrhistogram\HdrHistogram\2.1.9\HdrHistogram-2.1.9.jar;D:\repository\javax\inject\javax.inject\1\javax.inject-1.jar;D:\repository\io\reactivex\rxnetty\0.4.9\rxnetty-0.4.9.jar;D:\repository\com\netflix\ribbon\ribbon-core\2.3.0\ribbon-core-2.3.0.jar;D:\repository\commons-lang\commons-lang\2.6\commons-lang-2.6.jar;D:\repository\com\netflix\ribbon\ribbon-httpclient\2.3.0\ribbon-httpclient-2.3.0.jar;D:\repository\commons-collections\commons-collections\3.2.2\commons-collections-3.2.2.jar;D:\repository\com\sun\jersey\jersey-client\1.19.1\jersey-client-1.19.1.jar;D:\repository\com\sun\jersey\jersey-core\1.19.1\jersey-core-1.19.1.jar;D:\repository\javax\ws\rs\jsr311-api\1.1.1\jsr311-api-1.1.1.jar;D:\repository\com\sun\jersey\contribs\jersey-apache-client4\1.19.1\jersey-apache-client4-1.19.1.jar;D:\repository\com\netflix\servo\servo-core\0.12.21\servo-core-0.12.21.jar;D:\repository\com\netflix\netflix-commons\netflix-commons-util\0.3.0\netflix-commons-util-0.3.0.jar;D:\repository\com\netflix\ribbon\ribbon-loadbalancer\2.3.0\ribbon-loadbalancer-2.3.0.jar;D:\repository\com\netflix\netflix-commons\netflix-statistics\0.1.1\netflix-statistics-0.1.1.jar;D:\repository\io\reactivex\rxjava\1.3.8\rxjava-1.3.8.jar;D:\repository\com\alibaba\cloud\spring-cloud-starter-alibaba-nacos-config\2.2.6.RELEASE\spring-cloud-starter-alibaba-nacos-config-2.2.6.RELEASE.jar;D:\repository\mysql\mysql-connector-java\5.1.47\mysql-connector-java-5.1.47.jar;D:\repository\com\alibaba\druid-spring-boot-starter\1.2.25\druid-spring-boot-starter-1.2.25.jar;D:\repository\com\alibaba\druid\1.2.25\druid-1.2.25.jar;D:\repository\org\slf4j\slf4j-api\1.7.30\slf4j-api-1.7.30.jar;D:\repository\org\springframework\boot\spring-boot-autoconfigure\2.3.9.RELEASE\spring-boot-autoconfigure-2.3.9.RELEASE.jar;D:\repository\org\glassfish\jaxb\jaxb-runtime\2.3.3\jaxb-runtime-2.3.3.jar;D:\repository\org\glassfish\jaxb\txw2\2.3.3\txw2-2.3.3.jar;D:\repository\com\sun\istack\istack-commons-runtime\3.0.11\istack-commons-runtime-3.0.11.jar;D:\repository\com\sun\activation\jakarta.activation\1.2.2\jakarta.activation-1.2.2.jar;D:\repository\org\mybatis\spring\boot\mybatis-spring-boot-starter\2.1.1\mybatis-spring-boot-starter-2.1.1.jar;D:\repository\org\springframework\boot\spring-boot-starter-jdbc\2.3.9.RELEASE\spring-boot-starter-jdbc-2.3.9.RELEASE.jar;D:\repository\com\zaxxer\HikariCP\3.4.5\HikariCP-3.4.5.jar;D:\repository\org\springframework\spring-jdbc\5.2.13.RELEASE\spring-jdbc-5.2.13.RELEASE.jar;D:\repository\org\springframework\spring-tx\5.2.13.RELEASE\spring-tx-5.2.13.RELEASE.jar;D:\repository\org\mybatis\spring\boot\mybatis-spring-boot-autoconfigure\2.1.1\mybatis-spring-boot-autoconfigure-2.1.1.jar;D:\repository\org\mybatis\mybatis\3.5.3\mybatis-3.5.3.jar;D:\repository\org\mybatis\mybatis-spring\2.0.3\mybatis-spring-2.0.3.jar;D:\repository\org\projectlombok\lombok\1.18.18\lombok-1.18.18.jar;D:\repository\io\springfox\springfox-swagger2\2.9.2\springfox-swagger2-2.9.2.jar;D:\repository\io\swagger\swagger-annotations\1.5.20\swagger-annotations-1.5.20.jar;D:\repository\io\swagger\swagger-models\1.5.20\swagger-models-1.5.20.jar;D:\repository\io\springfox\springfox-spi\2.9.2\springfox-spi-2.9.2.jar;D:\repository\io\springfox\springfox-core\2.9.2\springfox-core-2.9.2.jar;D:\repository\io\springfox\springfox-schema\2.9.2\springfox-schema-2.9.2.jar;D:\repository\io\springfox\springfox-swagger-common\2.9.2\springfox-swagger-common-2.9.2.jar;D:\repository\io\springfox\springfox-spring-web\2.9.2\springfox-spring-web-2.9.2.jar;D:\repository\com\fasterxml\classmate\1.5.1\classmate-1.5.1.jar;D:\repository\org\springframework\plugin\spring-plugin-core\1.2.0.RELEASE\spring-plugin-core-1.2.0.RELEASE.jar;D:\repository\org\springframework\plugin\spring-plugin-metadata\1.2.0.RELEASE\spring-plugin-metadata-1.2.0.RELEASE.jar;D:\repository\org\mapstruct\mapstruct\1.2.0.Final\mapstruct-1.2.0.Final.jar;D:\repository\com\github\xiaoymin\knife4j-spring-ui\3.0.2\knife4j-spring-ui-3.0.2.jar;D:\repository\io\springfox\springfox-swagger-ui\2.9.2\springfox-swagger-ui-2.9.2.jar;D:\repository\cn\hutool\hutool-all\5.8.8\hutool-all-5.8.8.jar;D:\repository\com\ict\ycwl\common\1.0-SNAPSHOT\common-1.0-SNAPSHOT.jar;D:\repository\org\apache\commons\commons-lang3\3.10\commons-lang3-3.10.jar;D:\repository\com\github\axet\kaptcha\0.0.9\kaptcha-0.0.9.jar;D:\repository\com\jhlabs\filters\2.0.235\filters-2.0.235.jar;D:\repository\com\auth0\java-jwt\3.4.0\java-jwt-3.4.0.jar;D:\repository\org\springframework\boot\spring-boot-starter-validation\2.3.2.RELEASE\spring-boot-starter-validation-2.3.2.RELEASE.jar;D:\repository\org\glassfish\jakarta.el\3.0.3\jakarta.el-3.0.3.jar;D:\repository\org\hibernate\validator\hibernate-validator\6.1.7.Final\hibernate-validator-6.1.7.Final.jar;D:\repository\jakarta\validation\jakarta.validation-api\2.0.2\jakarta.validation-api-2.0.2.jar;D:\repository\org\jboss\logging\jboss-logging\3.4.1.Final\jboss-logging-3.4.1.Final.jar;D:\repository\com\github\pagehelper\pagehelper-spring-boot-starter\1.4.6\pagehelper-spring-boot-starter-1.4.6.jar;D:\repository\com\github\pagehelper\pagehelper-spring-boot-autoconfigure\1.4.6\pagehelper-spring-boot-autoconfigure-1.4.6.jar;D:\repository\com\github\pagehelper\pagehelper\5.3.2\pagehelper-5.3.2.jar;D:\repository\com\github\jsqlparser\jsqlparser\4.5\jsqlparser-4.5.jar;D:\repository\com\baomidou\mybatis-plus-boot-starter\3.5.1\mybatis-plus-boot-starter-3.5.1.jar;D:\repository\com\baomidou\mybatis-plus\3.5.1\mybatis-plus-3.5.1.jar;D:\repository\com\baomidou\mybatis-plus-extension\3.5.1\mybatis-plus-extension-3.5.1.jar;D:\repository\com\baomidou\mybatis-plus-core\3.5.1\mybatis-plus-core-3.5.1.jar;D:\repository\com\baomidou\mybatis-plus-annotation\3.5.1\mybatis-plus-annotation-3.5.1.jar;D:\repository\org\apache\poi\poi\5.2.3\poi-5.2.3.jar;D:\repository\org\apache\commons\commons-collections4\4.4\commons-collections4-4.4.jar;D:\repository\commons-io\commons-io\2.11.0\commons-io-2.11.0.jar;D:\repository\com\zaxxer\SparseBitSet\1.2\SparseBitSet-1.2.jar;D:\repository\org\apache\logging\log4j\log4j-api\2.13.3\log4j-api-2.13.3.jar;D:\repository\org\apache\poi\poi-ooxml\5.2.3\poi-ooxml-5.2.3.jar;D:\repository\org\apache\poi\poi-ooxml-lite\5.2.3\poi-ooxml-lite-5.2.3.jar;D:\repository\org\apache\xmlbeans\xmlbeans\5.1.1\xmlbeans-5.1.1.jar;D:\repository\xml-apis\xml-apis\1.4.01\xml-apis-1.4.01.jar;D:\repository\org\apache\commons\commons-compress\1.21\commons-compress-1.21.jar;D:\repository\com\github\virtuald\curvesapi\1.07\curvesapi-1.07.jar;D:\repository\org\locationtech\jts\jts-core\1.19.0\jts-core-1.19.0.jar;D:\repository\org\locationtech\jts\io\jts-io-common\1.19.0\jts-io-common-1.19.0.jar;D:\repository\com\googlecode\json-simple\json-simple\1.1.1\json-simple-1.1.1.jar;D:\repository\com\alibaba\easyexcel\3.1.1\easyexcel-3.1.1.jar;D:\repository\com\alibaba\easyexcel-core\3.1.1\easyexcel-core-3.1.1.jar;D:\repository\com\alibaba\easyexcel-support\3.1.1\easyexcel-support-3.1.1.jar;D:\repository\org\apache\poi\poi-ooxml-schemas\4.1.2\poi-ooxml-schemas-4.1.2.jar;D:\repository\org\apache\commons\commons-csv\1.8\commons-csv-1.8.jar;D:\repository\org\ehcache\ehcache\3.8.1\ehcache-3.8.1.jar;D:\repository\com\google\guava\guava\33.4.0-jre\guava-33.4.0-jre.jar;D:\repository\com\google\guava\failureaccess\1.0.2\failureaccess-1.0.2.jar;D:\repository\com\google\guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar;D:\repository\com\google\code\findbugs\jsr305\3.0.2\jsr305-3.0.2.jar;D:\repository\org\checkerframework\checker-qual\3.43.0\checker-qual-3.43.0.jar;D:\repository\com\google\errorprone\error_prone_annotations\2.36.0\error_prone_annotations-2.36.0.jar;D:\repository\com\google\j2objc\j2objc-annotations\3.0.0\j2objc-annotations-3.0.0.jar;D:\repository\com\google\ortools\ortools-java\9.8.3296\ortools-java-9.8.3296.jar;D:\repository\com\google\ortools\ortools-linux-x86-64\9.8.3296\ortools-linux-x86-64-9.8.3296.jar;D:\repository\com\google\ortools\ortools-darwin-x86-64\9.8.3296\ortools-darwin-x86-64-9.8.3296.jar;D:\repository\com\google\ortools\ortools-linux-aarch64\9.8.3296\ortools-linux-aarch64-9.8.3296.jar;D:\repository\com\google\ortools\ortools-darwin-aarch64\9.8.3296\ortools-darwin-aarch64-9.8.3296.jar;D:\repository\net\java\dev\jna\jna-platform\5.13.0\jna-platform-5.13.0.jar;D:\repository\net\java\dev\jna\jna\5.13.0\jna-5.13.0.jar;D:\repository\com\google\protobuf\protobuf-java\3.13.0\protobuf-java-3.13.0.jar;D:\repository\com\google\ortools\ortools-win32-x86-64\9.8.3296\ortools-win32-x86-64-9.8.3296.jar;D:\repository\org\apache\commons\commons-math3\3.6.1\commons-math3-3.6.1.jar;D:\repository\io\jenetics\jenetics\7.2.0\jenetics-7.2.0.jar;D:\repository\com\fasterxml\jackson\core\jackson-databind\2.15.2\jackson-databind-2.15.2.jar;D:\repository\com\fasterxml\jackson\core\jackson-core\2.15.2\jackson-core-2.15.2.jar;D:\repository\com\fasterxml\jackson\core\jackson-annotations\2.15.2\jackson-annotations-2.15.2.jar;D:\repository\com\h2database\h2\1.4.200\h2-1.4.200.jar;D:\repository\org\optaplanner\optaplanner-core\7.73.0.Final\optaplanner-core-7.73.0.Final.jar;D:\repository\org\kie\kie-api\7.73.0.Final\kie-api-7.73.0.Final.jar;D:\repository\org\kie\soup\kie-soup-maven-support\7.73.0.Final\kie-soup-maven-support-7.73.0.Final.jar;D:\repository\org\kie\kie-internal\7.73.0.Final\kie-internal-7.73.0.Final.jar;D:\repository\org\drools\drools-core\7.73.0.Final\drools-core-7.73.0.Final.jar;D:\repository\org\kie\soup\kie-soup-xstream\7.73.0.Final\kie-soup-xstream-7.73.0.Final.jar;D:\repository\org\drools\drools-core-reflective\7.73.0.Final\drools-core-reflective-7.73.0.Final.jar;D:\repository\org\drools\drools-core-dynamic\7.73.0.Final\drools-core-dynamic-7.73.0.Final.jar;D:\repository\org\drools\drools-compiler\7.73.0.Final\drools-compiler-7.73.0.Final.jar;D:\repository\org\kie\kie-memory-compiler\7.73.0.Final\kie-memory-compiler-7.73.0.Final.jar;D:\repository\org\drools\drools-ecj\7.73.0.Final\drools-ecj-7.73.0.Final.jar;D:\repository\org\antlr\antlr-runtime\3.5.2\antlr-runtime-3.5.2.jar;D:\repository\org\drools\drools-mvel\7.73.0.Final\drools-mvel-7.73.0.Final.jar;D:\repository\org\mvel\mvel2\2.4.14.Final\mvel2-2.4.14.Final.jar;D:\repository\org\kie\soup\kie-soup-project-datamodel-commons\7.73.0.Final\kie-soup-project-datamodel-commons-7.73.0.Final.jar;D:\repository\org\kie\soup\kie-soup-commons\7.73.0.Final\kie-soup-commons-7.73.0.Final.jar;D:\repository\org\kie\soup\kie-soup-project-datamodel-api\7.73.0.Final\kie-soup-project-datamodel-api-7.73.0.Final.jar;D:\repository\org\drools\drools-canonical-model\7.73.0.Final\drools-canonical-model-7.73.0.Final.jar;D:\repository\org\drools\drools-model-compiler\7.73.0.Final\drools-model-compiler-7.73.0.Final.jar;D:\repository\com\github\javaparser\javaparser-core\3.23.1\javaparser-core-3.23.1.jar;D:\repository\org\drools\drools-mvel-parser\7.73.0.Final\drools-mvel-parser-7.73.0.Final.jar;D:\repository\org\drools\drools-mvel-compiler\7.73.0.Final\drools-mvel-compiler-7.73.0.Final.jar;D:\repository\org\reflections\reflections\0.9.11\reflections-0.9.11.jar;D:\repository\org\javassist\javassist\3.21.0-GA\javassist-3.21.0-GA.jar;D:\repository\com\thoughtworks\xstream\xstream\1.4.19\xstream-1.4.19.jar;D:\repository\io\github\x-stream\mxparser\1.2.2\mxparser-1.2.2.jar;D:\repository\xmlpull\xmlpull\1.1.3.1\xmlpull-1.1.3.1.jar;D:\repository\org\optaplanner\optaplanner-spring-boot-starter\7.73.0.Final\optaplanner-spring-boot-starter-7.73.0.Final.jar;D:\repository\org\optaplanner\optaplanner-spring-boot-autoconfigure\7.73.0.Final\optaplanner-spring-boot-autoconfigure-7.73.0.Final.jar;D:\repository\org\optaplanner\optaplanner-persistence-jackson\7.73.0.Final\optaplanner-persistence-jackson-7.73.0.Final.jar;D:\repository\org\optaplanner\optaplanner-persistence-common\7.73.0.Final\optaplanner-persistence-common-7.73.0.Final.jar;D:\repository\com\graphhopper\jsprit-core\1.8\jsprit-core-1.8.jar;D:\repository\com\uber\h3\3.7.3\h3-3.7.3.jar;"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="C:\Users\<USER>\Program Files\Java\jdk1.8.0_151\jre"/>
    <property name="basedir" value="D:\烟草\交接信息\源代码\ycwl-ms-v3.0\path-calculate"/>
    <property name="file.separator" value="\"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="java.awt.graphicsenv" value="sun.awt.Win32GraphicsEnvironment"/>
    <property name="surefire.real.class.path" value="C:\Users\<USER>\AppData\Local\Temp\surefire311204213725103928\surefirebooter7759666411478501378.jar"/>
    <property name="sun.boot.class.path" value="C:\Program Files\Java\jdk1.8.0_151\jre\lib\resources.jar;C:\Program Files\Java\jdk1.8.0_151\jre\lib\rt.jar;C:\Program Files\Java\jdk1.8.0_151\jre\lib\sunrsasign.jar;C:\Program Files\Java\jdk1.8.0_151\jre\lib\jsse.jar;C:\Program Files\Java\jdk1.8.0_151\jre\lib\jce.jar;C:\Program Files\Java\jdk1.8.0_151\jre\lib\charsets.jar;C:\Program Files\Java\jdk1.8.0_151\jre\lib\jfr.jar;C:\Program Files\Java\jdk1.8.0_151\jre\classes"/>
    <property name="user.script" value=""/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="java.runtime.version" value="1.8.0_151-b12"/>
    <property name="user.name" value="panda"/>
    <property name="path.separator" value=";"/>
    <property name="os.version" value="10.0"/>
    <property name="java.endorsed.dirs" value="C:\Program Files\Java\jdk1.8.0_151\jre\lib\endorsed"/>
    <property name="java.runtime.name" value="Java(TM) SE Runtime Environment"/>
    <property name="file.encoding" value="GBK"/>
    <property name="java.vm.name" value="Java HotSpot(TM) 64-Bit Server VM"/>
    <property name="localRepository" value="D:\repository"/>
    <property name="java.vendor.url.bug" value="http://bugreport.sun.com/bugreport/"/>
    <property name="java.io.tmpdir" value="C:\Users\<USER>\AppData\Local\Temp\"/>
    <property name="java.version" value="1.8.0_151"/>
    <property name="user.dir" value="D:\烟草\交接信息\源代码\ycwl-ms-v3.0\path-calculate"/>
    <property name="os.arch" value="amd64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="java.awt.printerjob" value="sun.awt.windows.WPrinterJob"/>
    <property name="sun.os.patch.level" value=""/>
    <property name="java.library.path" value="C:\Program Files\Java\jdk1.8.0_151\jre\bin;C:\Windows\Sun\Java\bin;C:\Windows\system32;C:\Windows;D:\Python312\Scripts\;D:\Python312\;D:\VMware\bin\;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Program Files\Oculus\Support\oculus-runtime;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\NVIDIA Corporation\NVIDIA NvDLISR;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;C:\Program Files\Java\jdk1.8.0_151\bin;D:\qq\Bin;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\MySQL\MySQL Server 8.0\bin\;C:\Program Files\Java\jdk1.8.0_151\bin;D:\apache-tomcat-10.0.12\bin;D:\微信web开发者工具\dll;%M2_HO;E%\bin;D:\Redis\;D:\;D:\Git\cmd;D:\TortoiseSVN\bin;C:\Program Files (x86)\Graphviz2.38\bin;C:\Program Files\dotnet\;C:\Program Files\MySQL\MySQL Shell 8.0\bin\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\Bandizip\;D:\maven\apache-maven-3.6.3\bin;C:\Users\<USER>\AppData\Roaming\npm;D:\;F:\QQGame\Hall.58319\;E:\cursor\resources\app\bin;."/>
    <property name="java.vm.info" value="mixed mode"/>
    <property name="java.vendor" value="Oracle Corporation"/>
    <property name="java.vm.version" value="25.151-b12"/>
    <property name="java.ext.dirs" value="C:\Program Files\Java\jdk1.8.0_151\jre\lib\ext;C:\Windows\Sun\Java\lib\ext"/>
    <property name="sun.io.unicode.encoding" value="UnicodeLittle"/>
    <property name="java.class.version" value="52.0"/>
  </properties>
  <testcase name="testBoundaryDataParsing" classname="com.ict.ycwl.pathcalculate.ConvexDataTest" time="0.068"/>
  <testcase name="testConvexDataParsing" classname="com.ict.ycwl.pathcalculate.ConvexDataTest" time="0.009"/>
  <testcase name="testNetworkfullingSolve" classname="com.ict.ycwl.pathcalculate.ConvexDataTest" time="0.363"/>
  <testcase name="testMultipleConvexData" classname="com.ict.ycwl.pathcalculate.ConvexDataTest" time="0.012"/>
</testsuite>