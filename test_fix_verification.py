#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证粤北卷烟物流管理系统色块数据修复效果
"""

import requests
import json
import time
import sys

def check_service_health():
    """检查服务健康状态"""
    try:
        response = requests.get("http://localhost:8083/actuator/health", timeout=5)
        if response.status_code == 200:
            health_data = response.json()
            status = health_data.get('status', 'UNKNOWN')
            print(f"✅ path-calculate服务状态: {status}")
            return status == 'UP'
        else:
            print(f"❌ 服务健康检查失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接到服务: {e}")
        return False

def test_color_convex_api():
    """测试色块数据API"""
    url = "http://localhost:8083/pathcalculate/path/getAllColourConvex"
    
    print(f"\n🔍 测试API: {url}")
    print("=" * 60)
    
    try:
        start_time = time.time()
        response = requests.get(url, timeout=30)
        end_time = time.time()
        
        print(f"⏱️ 响应时间: {end_time - start_time:.2f}秒")
        print(f"📊 HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print("✅ JSON解析成功")
                
                # 分析响应结构
                if isinstance(data, dict) and 'code' in data and 'data' in data:
                    code = data['code']
                    result_data = data['data']
                    msg = data.get('msg', '')
                    
                    print(f"📋 响应码: {code}")
                    print(f"📝 响应消息: {msg}")
                    
                    if code == 200:
                        print("✅ API调用成功")
                        
                        if isinstance(result_data, list):
                            print(f"📊 班组数量: {len(result_data)}")
                            
                            total_colors = 0
                            empty_groups = 0
                            
                            for i, group_data in enumerate(result_data):
                                if isinstance(group_data, list):
                                    color_count = len(group_data)
                                    total_colors += color_count
                                    
                                    if color_count == 0:
                                        empty_groups += 1
                                        print(f"⚠️ 班组 {i+1}: 无色块数据")
                                    else:
                                        print(f"✅ 班组 {i+1}: {color_count} 个色块")
                                        
                                        # 显示第一个色块的示例
                                        if group_data:
                                            sample = group_data[0]
                                            if len(sample) > 100:
                                                sample = sample[:100] + "..."
                                            print(f"   示例: {sample}")
                                else:
                                    print(f"❌ 班组 {i+1}: 数据格式异常")
                            
                            print("\n📈 统计结果:")
                            print(f"   总色块数: {total_colors}")
                            print(f"   空班组数: {empty_groups}")
                            print(f"   有数据班组数: {len(result_data) - empty_groups}")
                            
                            if total_colors > 0:
                                print("🎉 修复成功！色块数据已正常生成")
                                return True
                            else:
                                print("❌ 修复失败：所有班组都没有色块数据")
                                return False
                        else:
                            print(f"❌ 数据格式异常: {type(result_data)}")
                            return False
                    else:
                        print(f"❌ API返回错误码: {code}, 消息: {msg}")
                        return False
                else:
                    print("❌ 响应格式异常")
                    print(f"响应内容: {json.dumps(data, indent=2, ensure_ascii=False)}")
                    return False
                    
            except json.JSONDecodeError as e:
                print(f"❌ JSON解析失败: {e}")
                print(f"原始响应: {response.text[:500]}")
                return False
        else:
            print(f"❌ HTTP请求失败: {response.status_code}")
            print(f"响应内容: {response.text[:500]}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
        return False
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败")
        return False
    except Exception as e:
        print(f"❌ 未知错误: {e}")
        return False

def main():
    """主函数"""
    print("🔧 粤北卷烟物流管理系统 - 色块数据修复验证")
    print("=" * 60)
    
    # 1. 检查服务状态
    if not check_service_health():
        print("\n❌ 服务未启动或不健康，请先启动path-calculate服务")
        print("启动命令: cd path-calculate && mvn spring-boot:run")
        return False
    
    # 2. 测试API
    success = test_color_convex_api()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 验证结果: 修复成功！")
        print("✅ 色块数据已正常生成，前端应该能够正常显示")
    else:
        print("❌ 验证结果: 修复失败")
        print("💡 建议检查服务日志以获取更多信息")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
